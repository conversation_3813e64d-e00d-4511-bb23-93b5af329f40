import { RepositoryError, QueryError } from "../middlewares/errorHandler";
import { DatabaseErrorEnum as d } from "../enum/errors";
import { ResponseMessageEnum as m } from "../enum/response";
import { PetAttributes } from "../entities/pets.entity";
import { withMongoDB } from "../db/mongodb";

export class PetRepository {
    async findPetById(id: string): Promise<PetAttributes> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db.collection<PetAttributes>("pets").findOne(
                    {
                        id: id,
                        isDeleted: false,
                    },
                    { projection: { _id: 0 } }
                );

                if (!response) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);

                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async findAll(): Promise<PetAttributes[]> {
        try {
            return await withMongoDB(async (db) => {
                const filter = { isDeleted: false };
                const response = await db
                    .collection<PetAttributes>("pets")
                    .find(filter, {
                        projection: {
                            _id: 0,
                        },
                    })
                    .sort({ "petInfo.hn": 1 })
                    .toArray();
                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async findList(limit: number, page: number): Promise<{ rows: PetAttributes[]; count: number }> {
        try {
            return await withMongoDB(async (db) => {
                const filter = { isDeleted: false };
                const offset = (page - 1) * limit;

                const count = await db.collection<PetAttributes>("pets").countDocuments(filter);

                const rows = await db
                    .collection<PetAttributes>("pets")
                    .find(filter, {
                        projection: {
                            _id: 0,
                        },
                    })
                    .sort({ "petInfo.hn": 1 })
                    .skip(offset)
                    .limit(limit)
                    .toArray();

                return { rows, count };
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async findByHN(hn: string): Promise<PetAttributes | null> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db.collection<PetAttributes>("pets").findOne(
                    {
                        "petInfo.hn": hn,
                        isDeleted: false,
                    },
                    { projection: { _id: 0 } }
                );

                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async create(data: PetAttributes): Promise<PetAttributes> {
        try {
            return await withMongoDB(async (db) => {
                const petData = {
                    ...data,
                    isDeleted: false,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    deletedAt: null,
                };
                const createdPet = await db.collection<PetAttributes>("pets").insertOne(petData);

                if (!createdPet) {
                    throw new QueryError(m.INSERTION_FAILED);
                }

                const response = await db
                    .collection<PetAttributes>("pets")
                    .findOne({ id: petData.id }, { projection: { _id: 0 } });

                if (!response) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);

                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async update(id: string, data: Partial<PetAttributes>): Promise<boolean> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db.collection<PetAttributes>("pets").updateOne(
                    { id: id, isDeleted: false },
                    {
                        $set: {
                            ...data,
                            updatedAt: new Date(),
                        },
                    }
                );
                if (response.matchedCount === 0) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);
                return response.modifiedCount === 1;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async delete(id: string): Promise<boolean> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db.collection<PetAttributes>("pets").updateOne(
                    {
                        id: id,
                        isDeleted: false,
                    },
                    {
                        $set: {
                            isDeleted: true,
                            deletedAt: new Date(),
                        },
                    }
                );

                if (response.matchedCount === 0) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);

                return response.modifiedCount > 0;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }
}

export default new PetRepository();

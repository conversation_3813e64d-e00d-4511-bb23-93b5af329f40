import express, { NextFunction, Request, Response, Router } from "express";
import { ResponseTitleEnum as r, ResponseStatusCodeEnum as s, ResponseMessageEnum as m } from "../enum/response";
import { OwnerModule } from "../modules/owners/owners.module";
import { OwnerRequest, OwnerSearchRequest } from "../modules/owners/dto/request";
import { ListLimitConstants } from "../constants/defaultValue";

export class OwnerController {
    private readonly ownerModule: OwnerModule;
    public router: Router;
    constructor(ownerModule: OwnerModule) {
        this.ownerModule = ownerModule;
        this.router = express.Router();
        this.routes();
    }
    private routes() {
        this.router.get("/list", this.getList.bind(this));
        this.router.get("/filter", this.getAllWithFilter.bind(this));
        this.router.get("/:id", this.getOne.bind(this));
        this.router.get("/", this.getAll.bind(this));
        this.router.post("/", this.create.bind(this));
        this.router.put("/:id", this.update.bind(this));
        this.router.delete("/:id", this.delete.bind(this));
    }
    private async getOne(req: Request, res: Response, next: NextFunction) {
        try {
            const id = req.params.id;
            const response = await this.ownerModule.getOne(id);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
    private async getAll(req: Request, res: Response, next: NextFunction) {
        try {
            const response = await this.ownerModule.getAll();
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async getAllWithFilter(req: Request, res: Response, next: NextFunction) {
        try {
            const { searchText } = req.query as OwnerSearchRequest;
            const response = await this.ownerModule.getAllWithFilter(searchText);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async getList(req: Request, res: Response, next: NextFunction) {
        try {
            const limit = Number(req.query.limit) || ListLimitConstants.DEFAULT_LIMIT;
            const page = Number(req.query.page) || ListLimitConstants.DEFAULT_PAGE;
            const response = await this.ownerModule.getList(limit, page);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
    private async create(req: Request, res: Response, next: NextFunction) {
        try {
            const body = req.body as OwnerRequest;
            const response = await this.ownerModule.create(body);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async update(req: Request, res: Response, next: NextFunction) {
        try {
            const id = req.params.id;
            const data = req.body;
            const response = await this.ownerModule.update(id, data);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
    private async delete(req: Request, res: Response, next: NextFunction) {
        try {
            const id = req.params.id;
            const response = await this.ownerModule.delete(id);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
}

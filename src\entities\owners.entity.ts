import { ResponseMessageEnum as m } from "../enum/response";
import { EntityError, FormatValidationError } from "../middlewares/errorHandler";
import { validated } from "../utils/validations";
import { createConfig } from "../validations/owners/owners.validate.config";
import { idValidateConfig } from "../validations/uuid/uuid.validate.config";
import { v7 as uuidv7 } from "uuid";
import { FieldConfigType } from "../types/validate.type";

export type OwnerInfo = {
    firstName: string;
    lastName: string;
    phoneNumber: string;
    email: string;
}
export type OwnerAttributes = {
    id: string;
    ownerInfo: OwnerInfo;
    isDeleted?: boolean;
    deletedAt?: Date | null;
    createdAt?: Date;
    updatedAt?: Date; 
};

export class OwnerEntity {
    private readonly id: string | null;
    private readonly ownerData: OwnerAttributes;
    constructor(
        id: string | null = null, 
        preData: OwnerAttributes | null = null,
        customConfig?: Record<string, FieldConfigType>
    ) {
        this.id = id;
        this.ownerData = this.initialize(preData, customConfig);
    }

    private initialize(preData: OwnerAttributes | null, customConfig?: Record<string, FieldConfigType>): OwnerAttributes {
        try {
            this.validate(preData, customConfig);
            return {
                id: this.id ?? uuidv7(),
                ownerInfo: preData?.ownerInfo,
            } as OwnerAttributes;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new FormatValidationError(m.INVALID_FORMAT, error.errors);
            }
            throw new EntityError(m.ENTITY_INITAILIZE_ERROR, null);
        }
    }

    private validate(preData: OwnerAttributes | null, customConfig?: Record<string, FieldConfigType>): void {
        if (preData) this.validateOwnerData(preData, customConfig);
        if (this.id) this.validateId(this.id);
    }

    private validateOwnerData(data: OwnerAttributes, customConfig?: Record<string, FieldConfigType>): void {
        const schema = customConfig ?? createConfig;
        const result = validated(schema, data);
        if (!result.isValid) {
            throw new FormatValidationError(m.INVALID_FORMAT, result.data);
        }
    }

    private validateId(id: string): void {
        const result = validated(idValidateConfig, { id });
        if (!result.isValid) {
            throw new FormatValidationError(m.INVALID_FORMAT, result.data);
        }
    }

    public getId(): string {
        return this.id ?? this.ownerData.id;
    }

    public getAttributes(): OwnerAttributes {
        return this.ownerData;
    }
}

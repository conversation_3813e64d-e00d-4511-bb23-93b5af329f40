import { v7 as uuidv7 } from "uuid";
import {
    PetGenderTypeEnum,
    PetSpeciesEnum,
    PetLivingEnvironmentEnum,
    ActiveScoreVisualLevelEnum,
    ActiveScoreCalculationMethodEnum,
} from "../../../enum/pet";
import { VisitStepEnum, VisitStatusEnum } from "../../../enum/visit";
import { PetInfoRequest } from "../../../modules/pets/dto/request";
import { OwnerInfo } from "../../../entities/owners.entity";
import { DoctorInfo } from "../../../entities/doctors.entity";
import { PetWithOwnerResponse } from "../../../modules/pets/dto/response";

const validDoctorUUID = uuidv7();
const validOwnerUUID = uuidv7();
const validDiseaseUUID = uuidv7();
const validPetUUID = uuidv7();

const validPetInfo: PetInfoRequest = {
    hn: "HN0001",
    name: "Test Pet",
    gender: PetGenderTypeEnum.MALE,
    species: PetSpeciesEnum.DOG,
    breed: "pitbull",
    isMixedBreed: false,
    birthDate: "2025-01-01T12:00:00Z",
    weights: [
        {
            weight: 3.2,
            measuredDate: "2025-01-01T12:00:00Z",
        },
    ],
    livingEnvironment: PetLivingEnvironmentEnum.INDOOR,
    isNeutering: true,
    diseases: [
        {
            diseaseId: validDiseaseUUID,
            diseaseName: "HIV",
            validFrom: "2025-01-01T12:00:00Z",
            validTo: "2025-01-01T12:00:00Z",
            createdAt: "2025-01-01T12:00:00Z",
            updatedAt: "2025-01-01T12:00:00Z",
        },
    ],
    activeScore: {
        score: 5,
        calculationMethod: ActiveScoreCalculationMethodEnum.VISUAL,
        questionaires: null,
        visualLevel: ActiveScoreVisualLevelEnum.VERY_ACTIVE,
    },
};

const validOwnerInfo: OwnerInfo = {
    firstName: "John",
    lastName: "Doe",
    phoneNumber: "081234567890",
    email: "<EMAIL>",
};

const validDoctorInfo: DoctorInfo = {
    prefix: "Dr.",
    firstName: "Steve",
    lastName: "Rogers",
    phoneNumber: "**********",
    email: "<EMAIL>",
};

const validWithNewOwner = {
    ownerId: null,
    doctorId: validDoctorUUID,
    visitStep: VisitStepEnum.STEP_ONE,
    status: VisitStatusEnum.WAITING,
    petInfo: validPetInfo,
    ownerInfo: validOwnerInfo,
    doctorInfo: validDoctorInfo,
    petHealth: null,
    petRequirement: null,
    petNutrition: null,
    petProductMatch: null,
};

const validWithExistOwner = {
    ownerId: validOwnerUUID,
    doctorId: validDoctorUUID,
    visitStep: VisitStepEnum.STEP_ONE,
    status: VisitStatusEnum.WAITING,
    petInfo: validPetInfo,
    ownerInfo: null,
    doctorInfo: validDoctorInfo,
    petHealth: null,
    petRequirement: null,
    petNutrition: null,
    petProductMatch: null,
};

const petWithOwnerResponseMock: PetWithOwnerResponse = {
    pet: {
        id: validPetUUID,
        ownerId: validOwnerUUID,
        petInfo: validPetInfo,
        isDeleted: false,
        deletedAt: null,
        createdAt: new Date("2025-01-01T12:00:00Z"),
        updatedAt: new Date("2025-01-01T12:00:00Z"),
    },
    owner: {
        id: validOwnerUUID,
        ownerInfo: validOwnerInfo,
        isDeleted: false,
        deletedAt: null,
        createdAt: new Date("2025-01-01T12:00:00Z"),
        updatedAt: new Date("2025-01-01T12:00:00Z"),
    }
};

export const createFromPetMocks = {
    request: {
        validWithNewOwner: validWithNewOwner,
        validWithExistOwner: validWithExistOwner,
    },
    response: {
        success: {
            data: undefined,
        },
        petWithOwner: petWithOwnerResponseMock,
    },
};

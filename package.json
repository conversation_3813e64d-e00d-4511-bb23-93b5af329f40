{"name": "backend", "version": "1.0.0", "main": "index.ts", "scripts": {"dev": "ts-node-dev src/application/server.ts", "start": "ts-node src/application/server.ts", "build": "tsc", "serve": "node dist/index.js", "test": "jest --maxWorkers=2", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "author": "", "license": "ISC", "description": "", "devDependencies": {"@types/express": "^4.17.21", "@types/helmet": "^0.0.48", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.5.2", "@types/node-fetch": "^2.6.12", "@types/redis": "^4.0.10", "@types/sequelize": "^4.28.20", "express": "^4.19.2", "jest": "^29.7.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.5.4", "uuid": "^10.0.0"}, "dependencies": {"@types/bcrypt": "^5.0.2", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express-serve-static-core": "^4.19.5", "@types/http-errors": "^2.0.4", "@types/swagger-jsdoc": "^6.0.4", "@types/uuid": "^10.0.0", "bcrypt": "^5.1.1", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.4.5", "express-mongo-sanitize": "^2.2.0", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongodb": "^6.14.2", "mongoose": "^8.12.1", "node-fetch": "^2.7.0", "pg": "^8.12.0", "pg-hstore": "^2.3.4", "redis": "^4.7.0", "sequelize": "^6.37.3", "sqlite3": "^5.1.7", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "zod": "^3.24.1"}}
import {
    VisitRequest,
    CreateVisitFromPetRequest,
    PetInfoSubmitRequest,
    PetHealthSubmitRequest,
    PetRequirementSubmitRequest,
    PetNutritionSubmitRequest,
    PetProductMatchSubmitRequest,
    OrderSummarySubmitRequest,
} from "../../../modules/visits/dto/request";
import { VisitResponse, mapNutrientsResponse, VisitWithOwnerListResponse } from "../../../modules/visits/dto/response";
import { TestCase } from "../../utils/testRunner";
import { VisitModule } from "../../../modules/visits/visits.module";
import { createFromPetMocks } from "./visits.test.mock";

type MethodTypeMapper = {
    create: { input: { data: VisitRequest }; output: VisitResponse };
    createFromPet: { input: { data: CreateVisitFromPetRequest }; output: void };
    handlePetInfoStep: { input: PetInfoSubmitRequest; output: boolean };
    handlePetHealthStep: { input: PetHealthSubmitRequest; output: boolean };
    handlePetRequirementStep: { input: PetRequirementSubmitRequest; output: boolean };
    handlePetNutritionStep: { input: PetNutritionSubmitRequest; output: boolean };
    handlePetProductMatchStep: { input: PetProductMatchSubmitRequest; output: boolean };
    handleOrderSummaryStep: { input: OrderSummarySubmitRequest; output: boolean };
    getPersonalizedNutrients: { input: string; output: mapNutrientsResponse };
    getOne: { input: string; output: VisitResponse | null };
    getAll: { input: void; output: VisitResponse[] };
    getList: { input: { limit: number; page: number }; output: VisitWithOwnerListResponse };
    update: { input: { id: string; data: VisitRequest }; output: boolean };
    delete: { input: string; output: boolean };
};

type ModuleTestConfig = {
    name: string;
    testCases: {
        [K in keyof VisitModule]?: {
            cases: TestCase<MethodTypeMapper[K]["input"], MethodTypeMapper[K]["output"]>[];
        };
    };
};

export const mockVisitRepository = {
    create: jest.fn(),
};

jest.mock("../../../repositories/visits.repository", () => ({
    VisitRepository: jest.fn(() => mockVisitRepository),
}));

export const mockPetModule = {
    createWithOwner: jest.fn(),
};

jest.mock("../../../modules/pets/pets.module", () => ({
    PetModule: jest.fn(() => mockPetModule),
}));

const moduleInstance = new VisitModule();

export const moduleTestConfig: ModuleTestConfig = {
    name: "Visits",
    testCases: {
        createFromPet: {
            cases: [
                {
                    name: "[SUCCESS] should create a new visit with new pet and new owner",
                    setup: {
                        input: {
                            data: createFromPetMocks.request.validWithNewOwner,
                        },
                        mocks: () => {
                            mockVisitRepository.create.mockResolvedValue(createFromPetMocks.response.success.data);
                            mockPetModule.createWithOwner.mockResolvedValue(createFromPetMocks.response.petWithOwner);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.createFromPet(input.data);
                    },
                    expectation: {
                        result: createFromPetMocks.response.success.data,
                        error: null,
                        methodCalls: [
                            {
                                method: mockVisitRepository.create,
                                called: true,
                            },
                            {
                                method: mockPetModule.createWithOwner,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
    },
};

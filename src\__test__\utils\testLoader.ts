import path from "path";
import fs from "fs";

type TestModuleConfig = {
    moduleName: string;
    testCases: Record<string, any>;
};

/**
 * Discovers and loads test configurations from all module test files in the __test__/modules directory.
 *
 * @returns {TestModuleConfig[]} Array of discovered module configurations, each containing:
 *   - moduleName: The name of the module
 *   - testCases: The test cases configuration for the module
 *
 * @throws {Error} If the modules directory doesn't exist at the expected path
 *
 */
export function modulesLoader(): TestModuleConfig[] {
    const modulesPath = path.resolve(__dirname, "../modules"); // C:\Users\<USER>\work\pawcare\paw-care-web-backend\src\__test__\modules

    // Validate modules directory exists
    if (!fs.existsSync(modulesPath)) {
        throw new Error(`Modules directory not found at: ${modulesPath}`);
    }

    // const moduleFolders = fs.readdirSync(modulesPath); // [requirements, ...]
    const moduleFolders = ["visits"];

    const discoveredModules: TestModuleConfig[] = [];

    // Process each module folder
    for (const moduleFolder of moduleFolders) {
        try {
            const modulePath = path.join(modulesPath, moduleFolder); // C:\Users\<USER>\work\pawcare\paw-care-web-backend\src\__test__\modules\requirements
            const testConfigPath = path.join(modulePath, `${moduleFolder}.test.config.ts`); // C:\Users\<USER>\work\pawcare\paw-care-web-backend\src\__test__\modules\requirements\requirements.test.config.ts

            // Skip if no test config file exists
            if (!fs.existsSync(testConfigPath)) {
                console.warn(`Warning: No test config file found for module ${moduleFolder}`);
                continue;
            }

            // Import test config
            const testConfigExports = require(testConfigPath);

            // Validate required exports
            if (!testConfigExports.moduleTestConfig) {
                console.error(`Error: moduleTestConfig not found in ${moduleFolder}.test.config.ts`);
                continue;
            }

            const { moduleTestConfig } = testConfigExports;

            // Add to discovered modules WITHOUT setting up mocks
            discoveredModules.push({
                moduleName: moduleTestConfig.name,
                testCases: moduleTestConfig.testCases,
            });
        } catch (error) {
            console.error(`Error discovering module ${moduleFolder}:`, error);
        }
    }
    return discoveredModules;
}

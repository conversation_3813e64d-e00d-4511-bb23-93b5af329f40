import { z, <PERSON>od<PERSON><PERSON><PERSON><PERSON>, Zod<PERSON>rror, ZodString, ZodNumber, ZodBoolean, ZodObject, ZodDate } from "zod";
import { ResponseValidationTypeEnum as v, ResponseValidationMessageEnum as m } from "../enum/validate";
import { FieldConfigType } from "../types/validate.type";
import { NutrientTypeEnum } from "../enum/requirement";
import {
    PetGenderTypeEnum,
    PetSpeciesEnum,
    PetLivingEnvironmentEnum,
    ActiveScoreCalculationMethodEnum,
    ActiveScoreVisualLevelEnum,
} from "../enum/pet";
import { MappingAlgorithmEnum, PetAllergicEnum } from "../enum/visit";

class Validator {
    private readonly fieldName: string;
    private readonly config: FieldConfigType;

    constructor(fieldName: string, config: FieldConfigType) {
        this.fieldName = fieldName;
        this.config = config;
    }

    public getValidator(): ZodTypeAny {
        const { type, enumValues = [] } = this.config;

        // Object lookup instead of switch case
        const validatorMap: Record<string, () => ZodTypeAny> = {
            [v.UUID]: () => this.validateUUID(),
            [v.STRING]: () => this.validateRequiredString(),
            [v.NUMBER]: () => this.validateRequiredNumber(),
            [v.BOOLEAN]: () => this.validateRequiredBoolean(),
            [v.OBJECT]: () => this.validateObject(),
            [v.ENUM]: () => this.validateEnum(enumValues),
            [v.DATE]: () => this.validateDate(),
            [v.EMAIL]: () => this.validateEmail(),
            [v.PHONE]: () => this.validatePhone(),
            [v.LETTER_ONLY]: () => this.validateLettersOnly(),
            [v.NO_SPECIAL_CHARACTERS]: () => this.validateNoSpecialChars(),
            [v.NO_PROHIBITED_CHARACTERS]: () => this.validateNoProhibitedChars(),
            [v.PREFIX]: () => this.validatePrefix(),
            // Custom Object Schemas เดิม
            [v.RECIPE_DETAILS]: () => this.validateRecipeDetails(),
            [v.QUESTIONARE]: () => this.validateQuestionares(),
            [v.NUTRIENT_RECIPE]: () => this.validateNutrientsRecipe(),
            [v.NUTRIENT]: () => this.validateNutrients(),
            [v.PERSONAL_REQUIREMENTS]: () => this.validatePersonalRequirements(),
            [v.EXTRA_MEALS]: () => this.validateExtraMeals(),
            [v.PET_INFO]: () => this.validatePetInfo(),
            [v.OWNER_INFO]: () => this.validateOwnerInfo(),
            [v.DOCTOR_INFO]: () => this.validateDoctorInfo(),
            [v.PET_HEALTH]: () => this.validatePetHealth(),
            [v.PET_REQUIREMENT]: () => this.validatePetRequirement(),
            [v.PET_NUTRITION]: () => this.validatePetNutrition(),
            [v.PET_PRODUCT_MATCH]: () => this.validatePetProductMatch(),
            [v.WEIGHTS]: () => this.validateWeights(),
        };
        // Return the validator or default if not found
        const validatorFn = validatorMap[type];
        const baseSchema = validatorFn ? validatorFn() : z.string().min(1, "none type");
        return this.wrapOptionality(baseSchema);
    }

    private wrapOptionality(baseSchema: ZodTypeAny): ZodTypeAny {
        const { allowNull = false, required = false, array = false, min, max, minLength, maxLength } = this.config;

        if (baseSchema instanceof ZodString) {
            let zodConfig: ZodString = baseSchema;
            if (typeof minLength === "number") zodConfig = zodConfig.min(minLength, `${m.MIN_CHAR} (${minLength})`);
            if (typeof maxLength === "number") zodConfig = zodConfig.max(maxLength, `${m.MAX_CHAR} (${maxLength})`);
            baseSchema = zodConfig;
        }
        if (baseSchema instanceof ZodNumber) {
            let zodConfig: ZodNumber = baseSchema;
            if (typeof min === "number") zodConfig = zodConfig.min(min, `${m.MIN_NUMBER} (${min})`);
            if (typeof max === "number") zodConfig = zodConfig.max(max, `${m.MAX_NUMBER} (${max})`);
            baseSchema = zodConfig;
        }

        if (array && !baseSchema._def.typeName.endsWith("Array")) baseSchema = baseSchema.array();
        if (!required) baseSchema = baseSchema.optional();
        if (allowNull) baseSchema = baseSchema.nullable();

        return baseSchema;
    }
    //Required
    private validateRequiredString(): ZodString {
        let base = z.string({ invalid_type_error: m.STRING, required_error: m.REQUIRED });
        return base;
    }
    private validateRequiredNumber(): ZodNumber {
        let base = z.number({ invalid_type_error: m.NUMBER, required_error: m.REQUIRED });
        return base;
    }
    private validateRequiredBoolean(): ZodBoolean {
        let base = z.boolean({ invalid_type_error: m.BOOLEAN, required_error: m.REQUIRED });
        return base;
    }
    private validateRequiredDate(): ZodDate {
        let base = z.date({ invalid_type_error: m.DATE, required_error: m.REQUIRED });
        return base;
    }
    //Enum
    private validateEnum(enumValues: string[]): z.ZodEffects<z.ZodString, string, string> {
        let base = this.validateRequiredString().refine((val) => enumValues.includes(val), {
            message: m.ENUM,
        });
        return base;
    }
    //Email
    private validateEmail(): ZodString {
        let base = this.validateRequiredString().email(m.EMAIL);
        return base;
    }
    private validateUUID(): ZodString {
        // UUID validation
        // This regex checks for both v4 and v7 UUIDs
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[47][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        let base = this.validateRequiredString().regex(uuidRegex, m.UUID);
        return base;
    }

    //Phone
    private validatePhone(): ZodString {
        const phoneRegex = /^(08|09|06)\d{8}$|^(02|03|04|05|07)\d{7}$/;
        let base = this.validateRequiredString().regex(phoneRegex, m.PHONE);
        return base;
    }

    //Letters
    private validateLettersOnly(): ZodString {
        const pattern = /^[a-zA-Z\u0E00-\u0E7F\s]*$/;
        let base = this.validateRequiredString().regex(pattern, m.LETTER_ONLY);
        return base;
    }

    //No Special
    private validateNoSpecialChars(): ZodString {
        const pattern = /^[a-zA-Z0-9\u0E00-\u0E7F\s]*$/;
        let base = this.validateRequiredString().regex(pattern, m.NO_SPECIAL_CHARACTERS);
        return base;
    }

    //No Prohibited characters
    private validateNoProhibitedChars(): ZodString {
        const pattern = /^[a-zA-Z0-9\u0E00-\u0E7F._-]*$/; // This regex allows letters, numbers, Thai characters, and the characters . _ -
        let base = this.validateRequiredString().regex(pattern, m.NO_PROHIBITED_CHARACTERS);
        return base;
    }

    // Date (If string, convert to Date)
    private validateDate(): ZodTypeAny {
        let base = z.union(
            [
                this.validateRequiredString()
                    .datetime({ message: m.DATE })
                    .transform((str) => new Date(str)),
                this.validateRequiredDate(),
            ],
            {
                required_error: m.REQUIRED,
            }
        );
        return base;
    }

    private validateObject(): ZodObject<Record<string, ZodTypeAny>> {
        if (this.config.objectConfig) {
            return ValidationService.createSchema(this.config.objectConfig);
        }
        console.warn(`Field '${this.fieldName}' has type '${v.OBJECT}' but no 'objectConfig' was provided. `);
        return z.object({});
    }

    // ------------- Custom Object -------------

    // RECIPE_DETAILS
    private validateRecipeDetails(): ZodTypeAny {
        return z
            .array(
                z.object({
                    recipeName: this.validateRequiredString(),
                    matchPercentage: this.validateRequiredNumber().min(0).max(100),
                    recipeNutrients: z
                        .array(
                            z.object({
                                nutrientName: this.validateRequiredString(),
                                unit: this.validateRequiredString(),
                                type: z.nativeEnum(NutrientTypeEnum),
                                min: this.validateRequiredNumber().min(0),
                                max: this.validateRequiredNumber().min(0).max(999999),
                                isInRange: this.validateRequiredBoolean(),
                            })
                        )
                        .min(1),
                    additives: z
                        .array(
                            z.object({
                                rawMatName: z.string().min(1),
                                rawMatAmount: z.number().min(0),
                                rawMatNutrients: z
                                    .array(
                                        z.object({
                                            nutrientName: z.string().min(1),
                                            unit: z.string().min(1),
                                            type: z.nativeEnum(NutrientTypeEnum),
                                            value: z.number().min(0),
                                        })
                                    )
                                    .min(1),
                            })
                        )
                        .nullable(),
                    recipeDetails: z.object({
                        me: z.number().min(0),
                        dailyAmount: z.number().min(0),
                        mealsPerDay: z.number().min(0),
                        amountPerMeal: z.number().min(0),
                        batchMakingFor: z.number().min(0),
                    }),
                })
            )
            .min(1);
    }

    // QUESTIONAIRE
    private validateQuestionares(): ZodTypeAny {
        return z
            .array(
                z.object({
                    title: z.string().min(1),
                    section: z.string().min(1),
                    order: z.number().int().positive(),
                    questions: z
                        .array(
                            z.object({
                                index: z.number().int().positive(),
                                text: z.string().min(1),
                                score: z.number().min(0).max(1),
                                isSelected: z.boolean(),
                            })
                        )
                        .min(1),
                })
            )
            .min(1);
    }

    // NUTRIENT_RECIPE
    private validateNutrientsRecipe(): ZodTypeAny {
        return z
            .array(
                z.object({
                    nutrientName: z.string().min(1),
                    unit: z.string().min(1),
                    type: z.nativeEnum(NutrientTypeEnum),
                    value: z.number().min(0),
                })
            )
            .min(1);
    }

    // NUTRIENT
    private validateNutrients(): ZodTypeAny {
        return z
            .array(
                z.object({
                    nutrientName: this.validateRequiredString(),
                    unit: this.validateRequiredString(),
                    type: z.nativeEnum(NutrientTypeEnum),
                    min: this.validateRequiredNumber().min(0),
                    max: this.validateRequiredNumber().min(0).max(999999),
                })
            )
            .min(1);
    }

    // PERSONAL_REQUIREMENTS
    private validatePersonalRequirements(): ZodTypeAny {
        return z.object({
            mappingMethod: z.nativeEnum(MappingAlgorithmEnum),
            requirements: z.array(z.string().min(1)).min(1),
        });
    }

    // EXTRA_MEALS
    private validateExtraMeals(): ZodTypeAny {
        return z
            .array(
                z.object({
                    foodName: z.string().min(1),
                    brand: z.string().min(1),
                    dailyAmount: z.number().min(0),
                    nutrients: z.object({
                        protein: z.number().nullable(),
                        fat: z.number().nullable(),
                        sodium: z.number().nullable(),
                        sugars: z.number().nullable(),
                        energy: z.number().nullable(),
                        water: z.number().nullable(),
                    }),
                })
            )
            .min(1);
    }

    // PET_INFO
    private validatePetInfo(): ZodTypeAny {
        return z.object({
            hn: z.string().min(1),
            name: z.string().min(1),
            gender: z.nativeEnum(PetGenderTypeEnum),
            species: z.nativeEnum(PetSpeciesEnum),
            breed: z.string().min(1),
            isMixedBreed: z.boolean(),
            birthDate: this.validateDate().nullable(),
            weights: z
                .array(
                    z.object({
                        weight: z.number().min(0),
                        measuredDate: this.validateDate(),
                    })
                )
                .min(1),
            livingEnvironment: z.nativeEnum(PetLivingEnvironmentEnum),
            isNeutering: z.boolean(),
            diseases: z.array(
                z.object({
                    diseaseId: z.string().min(1),
                    diseaseName: z.string().min(1),
                    validFrom: z.date(),
                    validTo: z.date(),
                    createdAt: z.date(),
                    updatedAt: z.date(),
                })
            ),
            activeScore: z.object({
                score: z.number().min(0),
                calculationMethod: z.nativeEnum(ActiveScoreCalculationMethodEnum),
                questionaires: z
                    .array(
                        z.object({
                            title: z.string().min(1),
                            section: z.string().min(1),
                            order: z.number().int().positive(),
                            questions: z
                                .array(
                                    z.object({
                                        index: z.number().int().positive(),
                                        text: z.string().min(1),
                                        score: z.number().min(0).max(1),
                                        isSelected: z.boolean(),
                                    })
                                )
                                .min(1),
                        })
                    )
                    .min(1)
                    .nullable(),
                visualLevel: z.nativeEnum(ActiveScoreVisualLevelEnum).nullable(),
            }),
        });
    }

    // OWNER_INFO
    private validateOwnerInfo(): ZodTypeAny {
        return z.object({
            firstName: z.string().min(1),
            lastName: z.string().min(1),
            phoneNumber: z.string().min(1),
            email: z.string().email().nullable(),
        });
    }

    // DOCTOR_INFO
    private validateDoctorInfo(): ZodTypeAny {
        return z.object({
            prefix: z.string().min(1),
            firstName: z.string().min(1),
            lastName: z.string().min(1),
            phoneNumber: z.string().min(1),
            email: z.string().email().nullable(),
        });
    }

    // PET_HEALTH
    private validatePetHealth(): ZodTypeAny {
        return z.object({
            currentBcs: z.number().min(0),
            idealBcs: z.number().min(0),
            currentWeight: z.number().min(0),
            idealWeight: z.number().min(0),
            questionaires: z
                .array(
                    z.object({
                        title: z.string().min(1),
                        section: z.string().min(1),
                        order: z.number().int().positive(),
                        questions: z
                            .array(
                                z.object({
                                    index: z.number().int().positive(),
                                    text: z.string().min(1),
                                    score: z.number().min(0).max(1),
                                    isSelected: z.boolean(),
                                })
                            )
                            .min(1),
                    })
                )
                .min(1),
        });
    }

    // PET_REQUIREMENT
    private validatePetRequirement(): ZodTypeAny {
        return z.object({
            diseases: z.array(
                z.object({
                    diseaseId: z.string().min(1),
                    diseaseName: z.string().min(1),
                    validFrom: this.validateDate(),
                    validTo: this.validateDate(),
                    createdAt: this.validateDate(),
                    updatedAt: this.validateDate(),
                })
            ),
            requirementsInfo: z.object({
                mappingMethod: z.nativeEnum(MappingAlgorithmEnum),
                profileRef: z.string().min(1),
                requirements: z.array(z.string().min(1)).min(1),
            }),
            nutritionNeeds: z.string().min(1),
            foodsHistory: z.object({
                extraMeals: z.array(
                    z.object({
                        foodName: z.string().min(1),
                        brand: z.string().min(1),
                        dailyAmount: z.number().min(0),
                        compositions: z
                            .object({
                                protein: z.number().nullable(),
                                fat: z.number().nullable(),
                                fiber: z.number().nullable(),
                                moisture: z.number().nullable(),
                                sodium: z.number().nullable(),
                                ash: z.number().nullable(),
                                me: z.number().nullable(),
                            })
                            .nullable(),
                    })
                ),
                allergic: z.array(z.nativeEnum(PetAllergicEnum)),
                foodsToAvoid: z.array(z.nativeEnum(PetAllergicEnum)),
            }),
        });
    }

    // PET_NUTRITION
    private validatePetNutrition(): ZodTypeAny {
        return z.object({
            personalNutrients: z
                .array(
                    z.object({
                        nutrientName: z.string().min(1),
                        unit: z.string().min(1),
                        type: z.nativeEnum(NutrientTypeEnum),
                        min: z.number().min(0),
                        max: z.number().min(0).max(999999),
                    })
                )
                .min(1),
            energyRequirements: z.object({
                stomachSize: z.number().min(0),
                weightTypeSelected: z.string().min(1),
                factor: z.number().min(0),
                rer: z.number().min(0),
                der: z.number().min(0),
            }),
        });
    }

    // PET_PRODUCT_MATCH
    private validatePetProductMatch(): ZodTypeAny {
        return z.object({
            matchingRecipes: z
                .array(
                    z.object({
                        recipeName: z.string().min(1),
                        matchPercentage: z.number().min(0).max(100),
                        recipeNutrients: z
                            .array(
                                z.object({
                                    nutrientName: z.string().min(1),
                                    unit: z.string().min(1),
                                    type: z.nativeEnum(NutrientTypeEnum),
                                    min: z.number().min(0),
                                    max: z.number().min(0).max(999999),
                                    isInRange: z.boolean(),
                                })
                            )
                            .min(1),
                        additives: z
                            .array(
                                z.object({
                                    rawMatName: z.string().min(1),
                                    rawMatAmount: z.number().min(0),
                                    rawMatNutrients: z
                                        .array(
                                            z.object({
                                                nutrientName: z.string().min(1),
                                                unit: z.string().min(1),
                                                type: z.nativeEnum(NutrientTypeEnum),
                                                value: z.number().min(0),
                                            })
                                        )
                                        .min(1),
                                })
                            )
                            .nullable(),
                        recipeDetails: z.object({
                            me: z.number().min(0),
                            dailyAmount: z.number().min(0),
                            mealsPerDay: z.number().min(0),
                            amountPerMeal: z.number().min(0),
                            batchMakingFor: z.number().min(0),
                        }),
                    })
                )
                .min(1),
        });
    }

    // WEIGHTS
    private validateWeights(): ZodTypeAny {
        return z
            .array(
                z.object({
                    weight: z.number().min(0),
                    measuredDate: this.validateDate(),
                })
            )
            .min(1);
    }

    // PREFIX
    private validatePrefix(): ZodString {
        const pattern = /^[a-zA-Z\u0E00-\u0E7F.\s]*$/;
        let base = z.string({ required_error: m.REQUIRED }).regex(pattern, m.PREFIX);
        return base;
    }
}

class ValidationService {
    public static createSchema(fields: Record<string, FieldConfigType>): ZodObject<Record<string, ZodTypeAny>> {
        const schemaObject = Object.entries(fields).reduce((acc, [field, config]) => {
            const validator = new Validator(field, config);
            acc[field] = validator.getValidator();
            return acc;
        }, {} as Record<string, ZodTypeAny>);

        return z.object(schemaObject);
    }

    private static buildNestedError(formattedErrors: Record<string, any>, path: (string | number)[], message: string) {
        if (path.length === 0) return;

        const [key, ...restOfPath] = path;
        const isArrayKey = typeof key === "number";

        if (isArrayKey) return;

        const nextIsNumber = typeof restOfPath[0] === "number";

        if (nextIsNumber) {
            formattedErrors[key] ??= [];
            const arr = formattedErrors[key] as any[];

            const index = restOfPath[0];
            let existingItem = arr.find((item) => item._index === index);

            if (!existingItem) {
                existingItem = { _index: index };
                arr.push(existingItem);
            }

            if (restOfPath.length === 1) {
                existingItem["_error"] = message;
            } else {
                ValidationService.buildNestedError(existingItem, restOfPath.slice(1), message);
            }
        } else {
            if (restOfPath.length === 0) {
                formattedErrors[key] = message;
            } else {
                formattedErrors[key] ??= {};
                ValidationService.buildNestedError(formattedErrors[key], restOfPath, message);
            }
        }
    }

    public static validate<
        U extends Record<string, FieldConfigType>,
        T extends Record<string, string | number | boolean | Date | string[] | object[] | object | null>
    >(fields: U, data: T) {
        const schema = this.createSchema(fields);
        try {
            const validatedData = schema.parse(data);
            return { isValid: true, data: validatedData, errors: null };
        } catch (err) {
            if (err instanceof ZodError) {
                const formattedErrors: Record<string, any> = {};

                err.errors.forEach((e) => {
                    this.buildNestedError(formattedErrors, e.path, e.message);
                });

                return { isValid: false, data: formattedErrors, errors: null };
            }
            throw err;
        }
    }
}

// Export the main validation function
export const validated = <
    U extends Record<string, FieldConfigType>,
    T extends Record<string, string | number | boolean | Date | string[] | object[] | object | null>
>(
    fields: U,
    data: T
) => {
    return ValidationService.validate(fields, data);
};

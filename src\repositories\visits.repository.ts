import { QueryError, RepositoryError } from "../middlewares/errorHandler";
import { VisitStepEnum, VisitStatusEnum } from "../enum/visit";
import { DatabaseErrorEnum as d } from "../enum/errors";
import { ResponseMessageEnum as m } from "../enum/response";
import { VisitAttributes } from "../entities/visits.entity";
import { PetInfo } from "../entities/pets.entity";
import { OwnerInfo } from "../entities/owners.entity";
import { withMongoDB } from "../db/mongodb";
import { DoctorInfo } from "../entities/doctors.entity";

interface VisitWithOwnerDetail {
    id: string;
    petId: string;
    doctorId: string;
    petInfo: PetInfo;
    doctorInfo: DoctorInfo;
    ownerInfo: OwnerInfo;
    visitStep: VisitStepEnum;
    status: VisitStatusEnum;
}

export class VisitRepository {
    async findVisitById(id: string): Promise<VisitAttributes> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db.collection<VisitAttributes>("visits").findOne(
                    {
                        id: id,
                        isDeleted: false,
                    },
                    { projection: { _id: 0 } }
                );

                if (!response) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);

                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async findAll(): Promise<VisitAttributes[]> {
        try {
            return await withMongoDB(async (db) => {
                const filter = { isDeleted: false };
                const response = await db
                    .collection<VisitAttributes>("visits")
                    .find(filter, {
                        projection: {
                            _id: 0,
                        },
                    })
                    .toArray();
                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async findList(limit: number, page: number): Promise<{ rows: VisitWithOwnerDetail[]; count: number }> {
        try {
            return await withMongoDB(async (db) => {
                let filter: any = { isDeleted: false };
                const offset = (page - 1) * limit;

                const count = await db.collection<VisitAttributes>("visits").countDocuments(filter);

                const rows = await db
                    .collection<VisitAttributes>("visits")
                    .aggregate<VisitWithOwnerDetail>([
                        { $match: filter },
                        // Purpose: Join visits with pets collection to get pet information
                        {
                            $lookup: {
                                from: "pets", // Join with pets collection
                                localField: "petId", // Field in visits collection
                                foreignField: "id", // Field in pets collection
                                as: "petData", // Name for joined data array
                            },
                        },
                        // Purpose: Convert petData array into individual documents
                        // Result: Each visit document now has pet info as petData object
                        { $unwind: "$petData" },
                        // Purpose: Join with owners collection using the petData's ownerId
                        {
                            $lookup: {
                                from: "owners",
                                localField: "petData.ownerId",
                                foreignField: "id",
                                as: "ownerData",
                            },
                        },
                        // Purpose: Convert ownerData array into individual documents
                        // Result: Each visit now has owner info as ownerData object
                        { $unwind: "$ownerData" },
                        // Purpose: Extract the ownerInfo nested object from ownerData and Restructure document with desired field order
                        {
                            $project: {
                                _id: 0,
                                id: "$id",
                                petId: "$petId",
                                doctorId: "$doctorId",
                                petInfo: {
                                    hn: "$petInfo.hn",
                                    name: "$petInfo.name",
                                    gender: "$petInfo.gender",
                                    species: "$petInfo.species",
                                    breed: "$petInfo.breed",
                                },
                                doctorInfo: {
                                    prefix: "$doctorInfo.prefix",
                                    firstName: "$doctorInfo.firstName",
                                    lastName: "$doctorInfo.lastName",
                                },
                                ownerInfo: "$ownerData.ownerInfo",
                                visitStep: "$visitStep",
                                status: "$status",
                                createdAt: "$createdAt",
                            },
                        },
                        // Sort by creation date, newest first
                        { $sort: { createdAt: -1 } },
                        { $skip: offset },
                        { $limit: limit },
                    ])
                    .toArray();
                return { rows, count };
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async create(data: VisitAttributes): Promise<VisitAttributes> {
        try {
            return await withMongoDB(async (db) => {
                const visitData = {
                    ...data,
                    isDeleted: false,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    deletedAt: null,
                };
                const createdVisit = await db.collection<VisitAttributes>("visits").insertOne(visitData);

                if (!createdVisit) {
                    throw new QueryError(m.INSERTION_FAILED);
                }

                const response = await this.findVisitById(visitData.id);

                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async update(id: string, data: Partial<VisitAttributes>): Promise<boolean> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db.collection<VisitAttributes>("visits").updateOne(
                    { id: id, isDeleted: false },
                    {
                        $set: {
                            ...data,
                            updatedAt: new Date(),
                        },
                    }
                );
                if (response.matchedCount === 0) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);
                return response.modifiedCount === 1;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }
}

export default new VisitRepository();

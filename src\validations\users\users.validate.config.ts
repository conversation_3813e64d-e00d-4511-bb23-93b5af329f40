import { FieldConfigMap } from "../../types/validate.type";
import { UserValidateType, UserCredentialsValidateType, UserFilterRequest } from "./users.type";
import { ResponseValidationTypeEnum as v } from "../../enum/validate";
import { CredentialsTypeEnum as t } from "../../enum/user";

export const createConfig: FieldConfigMap<UserValidateType> = {
    id: { type: v.UUID, required: false },
    username: { type: v.NO_PROHIBITED_CHARACTERS, required: true, minLength: 3, maxLength: 20 },
    firstName: { type: v.LETTER_ONLY, required: true, maxLength: 255 },
    lastName: { type: v.LETTER_ONLY, required: true, maxLength: 255 },
    email: { type: v.EMAIL, required: true },
    roles: { type: v.LETTER_ONLY, required: false, array: true },
};

export const receiveConfig: FieldConfigMap<UserValidateType> = {
    id: { type: v.UUID, required: true },
    username: { type: v.NO_PROHIBITED_CHARACTERS, required: true, minLength: 3, maxLength: 20 },
    firstName: { type: v.LETTER_ONLY, required: true, maxLength: 255 },
    lastName: { type: v.LETTER_ONLY, required: true, maxLength: 255 },
    email: { type: v.EMAIL, required: true },
    roles: { type: v.LETTER_ONLY, required: false, array: true },
};

export const userCredentialsConfig: FieldConfigMap<UserCredentialsValidateType> = {
    type: { type: v.ENUM, required: true, enumValues: [t.PASSWORD, t.OTP] },
    value: { type: v.STRING, required: true, minLength: 3, maxLength: 20 },
    temporary: { type: v.BOOLEAN, required: false },
};

export const filterConfig: FieldConfigMap<UserFilterRequest> = {
    username: { type: v.NO_PROHIBITED_CHARACTERS, required: false, minLength: 3, maxLength: 20 },
    firstName: { type: v.LETTER_ONLY, required: false, maxLength: 255 },
    lastName: { type: v.LETTER_ONLY, required: false, maxLength: 255 },
    email: { type: v.EMAIL, required: false },
};

import { FieldConfigMap } from "../../types/validate.type";
import { PetValidateType, PetInfoValidateType } from "./pets.type";
import { ResponseValidationTypeEnum as v } from "../../enum/validate";

export const createConfig: FieldConfigMap<PetValidateType> = {
    ownerId: { type: v.UUID, required: true },
    petInfo: { type: v.PET_INFO, required: true },
};

export const petInfoConfig: FieldConfigMap<PetInfoValidateType> = {
    petInfo: { type: v.PET_INFO, required: true },
};

import { OwnerAttributes, OwnerEntity } from "../../entities/owners.entity";
import {
    ModuleError,
    FormatValidationError,
    DefaultError,
    EntityError,
    ServiceError,
    RepositoryError,
} from "../../middlewares/errorHandler";
import { ErrorTypeEnum as e, DatabaseErrorEnum as d } from "../../enum/errors";
import { ResponseMessageEnum as m } from "../../enum/response";
import { OwnerRequest } from "./dto/request";
import { OwnerListResponse, OwnerResponse } from "./dto/response";
import { OwnerRepository } from "../../repositories/owners.repository";

export class OwnerModule {
    private readonly ownerRepository;
    constructor() {
        this.ownerRepository = new OwnerRepository();
    }
    public async getOne(id: string): Promise<OwnerResponse | null> {
        try {
            const ownerEntity = new OwnerEntity(id);
            const validId = ownerEntity.getId();

            const response = await this.ownerRepository.findOwnerById(validId);

            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_ONE_OWNER_FAILED);
        }
    }

    public async getAll(): Promise<OwnerResponse[]> {
        try {
            const response = await this.ownerRepository.findAll();
            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_ALL_OWNER_FAILED);
        }
    }

    public async getAllWithFilter(searchText: string): Promise<OwnerResponse[]> {
        try {
            const response = await this.ownerRepository.findAllWithFilter(searchText);
            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_ALL_OWNER_WITH_FILTER_FAILED);
        }
    }

    public async getList(limit: number, page: number): Promise<OwnerListResponse> {
        try {
            const response = await this.ownerRepository.findList(limit, page);
            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_LIST_OWNER_FAILED);
        }
    }

    public async create(data: OwnerRequest): Promise<OwnerResponse> {
        try {
            const ownerData = data as OwnerAttributes;
            const ownerEntity = new OwnerEntity(null, ownerData);
            const validData = ownerEntity.getAttributes();

            const existingUser = await this.ownerRepository.findByPhoneNumber(validData.ownerInfo.phoneNumber);
            if (existingUser) throw new ModuleError(m.DUPLICATED_HNID, d.DUPLICATE_HNID);

            const response = await this.ownerRepository.create(validData);

            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.CREATE_OWNER_FAILED);
        }
    }

    public async update(id: string, data: OwnerRequest): Promise<boolean> {
        try {
            const ownerData = data as OwnerAttributes;
            const ownerEntity = new OwnerEntity(id, ownerData);
            const validId = ownerEntity.getId();
            const validData = ownerEntity.getAttributes();

            const response = await this.ownerRepository.update(validId, validData);

            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.UPDATE_OWNER_FAILED);
        }
    }

    public async delete(id: string): Promise<boolean> {
        try {
            const ownerEntity = new OwnerEntity(id);
            const validId = ownerEntity.getId();

            const data = {
                isDeleted: true,
                deletedAt: new Date(),
            };

            const response = await this.ownerRepository.update(validId, data);

            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.DELETE_OWNER_FAILED);
        }
    }
}

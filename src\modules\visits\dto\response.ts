import { PetHealthSubmitType, PetRequirementSubmitType, PetNutritionSubmitType, PetProductMatchSubmitType } from "../../../entities/visits.entity";
import { PetInfo } from "../../../entities/pets.entity";
import { VisitStatusEnum, VisitStepEnum } from "../../../enum/visit";
import { NutrientTypeEnum } from "../../../enum/requirement";
import { OwnerInfo } from "../../../entities/owners.entity";
import { DoctorInfo } from "../../../entities/doctors.entity";

export type VisitResponse = {
    id?: string;
    petId: string;
    doctorId: string;
    petInfo: PetInfo;
    doctorInfo: DoctorInfo;
    visitStep: VisitStepEnum;
    status: VisitStatusEnum;
    petHealth: PetHealthSubmitType | null;
    petRequirement: PetRequirementSubmitType | null;
    petNutrition: PetNutritionSubmitType | null;
    petProductMatch: PetProductMatchSubmitType | null;
    isDeleted?: boolean;
    deletedAt?: Date | null;
    createdAt?: Date;
    updatedAt?: Date;
}

export type VisitListResponse = {
    rows: VisitResponse[];
    count: number;
}

export type VisitWithOwnerResponse = {
    id: string;
    petId: string;
    doctorId: string;
    petInfo: PetInfo;
    doctorInfo: DoctorInfo;
    ownerInfo: OwnerInfo;
    visitStep: VisitStepEnum;
    status: VisitStatusEnum;
}

export type VisitWithOwnerListResponse = {
    rows: VisitWithOwnerResponse[];
    count: number;
}

export type nutrientData = {
    nutrientName: string;
    unit: string;
    type: NutrientTypeEnum;
    min: number;
    max: number;
};

export type mapNutrientsResponse = {
    nutrients: nutrientData[];
};